/**
 * GoBackend-Kratos Bridge Service for HVAC-Remix
 * 
 * Server-side service for secure communication with GoBackend-Kratos
 * Handles authentication, error handling, and data transformation
 */

import { gobackendClient, handleGoBackendError, type GoBackendError } from '~/lib/gobackend-client';
import type { 
  Customer, 
  Job, 
  AIAnalysisRequest, 
  AIAnalysisResponse,
  Email,
  RealTimeMetrics,
  SystemHealth 
} from '~/types/gobackend-api';

/**
 * Configuration for GoBackend bridge
 */
const BRIDGE_CONFIG = {
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
  healthCheckInterval: 60000, // 1 minute
};

/**
 * Authentication context for server-side requests
 */
interface AuthContext {
  userId?: string;
  token?: string;
  roles?: string[];
}

/**
 * Bridge response wrapper
 */
interface BridgeResponse<T> {
  success: boolean;
  data?: T;
  error?: GoBackendError;
  metadata?: {
    requestId: string;
    timestamp: Date;
    latency: number;
  };
}

/**
 * Main GoBackend Bridge Service
 */
export class GoBackendBridge {
  private static instance: GoBackendBridge;
  private healthStatus: 'healthy' | 'unhealthy' | 'unknown' = 'unknown';
  private lastHealthCheck: Date = new Date();

  private constructor() {
    // Start health monitoring
    this.startHealthMonitoring();
  }

  static getInstance(): GoBackendBridge {
    if (!GoBackendBridge.instance) {
      GoBackendBridge.instance = new GoBackendBridge();
    }
    return GoBackendBridge.instance;
  }

  /**
   * Health monitoring
   */
  private async startHealthMonitoring() {
    setInterval(async () => {
      try {
        await this.checkHealth();
      } catch (error) {
        console.error('Health check failed:', error);
        this.healthStatus = 'unhealthy';
      }
    }, BRIDGE_CONFIG.healthCheckInterval);
  }

  async checkHealth(): Promise<SystemHealth> {
    const startTime = Date.now();
    
    try {
      const health = await gobackendClient.system.health.query();
      this.healthStatus = health.status === 'healthy' ? 'healthy' : 'unhealthy';
      this.lastHealthCheck = new Date();
      
      return health;
    } catch (error) {
      this.healthStatus = 'unhealthy';
      throw handleGoBackendError(error);
    }
  }

  /**
   * Generic request wrapper with retry logic
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    context?: AuthContext
  ): Promise<BridgeResponse<T>> {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    for (let attempt = 1; attempt <= BRIDGE_CONFIG.retries; attempt++) {
      try {
        const data = await operation();
        
        return {
          success: true,
          data,
          metadata: {
            requestId,
            timestamp: new Date(),
            latency: Date.now() - startTime,
          },
        };
      } catch (error) {
        const goBackendError = handleGoBackendError(error);
        
        // Don't retry on authentication or validation errors
        if (goBackendError.statusCode === 401 || goBackendError.statusCode === 400) {
          return {
            success: false,
            error: goBackendError,
            metadata: {
              requestId,
              timestamp: new Date(),
              latency: Date.now() - startTime,
            },
          };
        }

        // Last attempt - return error
        if (attempt === BRIDGE_CONFIG.retries) {
          return {
            success: false,
            error: goBackendError,
            metadata: {
              requestId,
              timestamp: new Date(),
              latency: Date.now() - startTime,
            },
          };
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, BRIDGE_CONFIG.retryDelay * attempt));
      }
    }

    // This should never be reached, but TypeScript requires it
    throw new Error('Unexpected error in executeWithRetry');
  }

  /**
   * Customer Service Bridge Methods
   */
  async getCustomers(filters?: {
    search?: string;
    status?: 'active' | 'inactive';
    region?: string;
    limit?: number;
    offset?: number;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.customer.list.query(filters),
      context
    );
  }

  async getCustomer(id: string, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.customer.get.query({ id }),
      context
    );
  }

  async createCustomer(data: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.customer.create.mutate(data),
      context
    );
  }

  async updateCustomer(id: string, data: Partial<Customer>, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.customer.update.mutate({ id, ...data }),
      context
    );
  }

  /**
   * Job Service Bridge Methods
   */
  async getJobs(filters?: {
    status?: Job['status'];
    customerId?: string;
    technicianId?: string;
    priority?: Job['priority'];
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    offset?: number;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.list.query(filters),
      context
    );
  }

  async getJob(id: string, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.get.query({ id }),
      context
    );
  }

  async createJob(data: Omit<Job, 'id' | 'createdAt' | 'updatedAt' | 'customer' | 'technician'>, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.create.mutate(data),
      context
    );
  }

  async updateJobStatus(id: string, status: Job['status'], context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.updateStatus.mutate({ id, status }),
      context
    );
  }

  async assignTechnician(jobId: string, technicianId: string, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.assignTechnician.mutate({ jobId, technicianId }),
      context
    );
  }

  /**
   * AI Service Bridge Methods
   */
  async analyzeCustomerIssue(data: AIAnalysisRequest, context?: AuthContext): Promise<BridgeResponse<AIAnalysisResponse>> {
    return this.executeWithRetry(
      () => gobackendClient.ai.analyzeIssue.mutate(data),
      context
    );
  }

  async chatWithAI(data: {
    message: string;
    context?: any;
    conversationId?: string;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.ai.chat.mutate(data),
      context
    );
  }

  async generateResponse(data: {
    customerMessage: string;
    context: any;
    tone?: 'professional' | 'friendly' | 'technical';
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.ai.generateResponse.mutate(data),
      context
    );
  }

  async predictMaintenance(data: {
    deviceId: string;
    deviceType: string;
    lastMaintenanceDate?: Date;
    usageData?: any;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.ai.predictMaintenance.mutate(data),
      context
    );
  }

  async optimizeRoute(data: {
    jobs: Array<{ id: string; location: any; priority: number }>;
    technicianLocation: any;
    constraints?: any;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.ai.optimizeRoute.mutate(data),
      context
    );
  }

  /**
   * Email Service Bridge Methods
   */
  async getEmails(filters?: {
    status?: Email['status'];
    category?: string;
    priority?: Email['priority'];
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    offset?: number;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.email.list.query(filters),
      context
    );
  }

  async analyzeEmail(emailId: string, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.email.analyze.mutate({ emailId }),
      context
    );
  }

  async sendEmail(data: {
    to: string[];
    subject: string;
    body: string;
    template?: string;
    attachments?: string[];
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.email.send.mutate(data),
      context
    );
  }

  /**
   * Analytics Service Bridge Methods
   */
  async getRealTimeMetrics(context?: AuthContext): Promise<BridgeResponse<RealTimeMetrics>> {
    return this.executeWithRetry(
      () => gobackendClient.analytics.getRealTimeMetrics.query(),
      context
    );
  }

  async getJobMetrics(filters?: {
    dateFrom?: Date;
    dateTo?: Date;
    groupBy?: 'day' | 'week' | 'month';
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.analytics.getJobMetrics.query(filters),
      context
    );
  }

  async getCustomerSatisfaction(filters?: {
    dateFrom?: Date;
    dateTo?: Date;
    customerId?: string;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.analytics.getCustomerSatisfaction.query(filters),
      context
    );
  }

  async getRevenue(filters?: {
    dateFrom?: Date;
    dateTo?: Date;
    groupBy?: 'day' | 'week' | 'month';
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.analytics.getRevenue.query(filters),
      context
    );
  }

  /**
   * Utility methods
   */
  getHealthStatus(): {
    status: 'healthy' | 'unhealthy' | 'unknown';
    lastCheck: Date;
  } {
    return {
      status: this.healthStatus,
      lastCheck: this.lastHealthCheck,
    };
  }

  async testConnection(): Promise<boolean> {
    try {
      const health = await this.checkHealth();
      return health.status === 'healthy';
    } catch {
      return false;
    }
  }
}

/**
 * Singleton instance export
 */
export const goBackendBridge = GoBackendBridge.getInstance();

/**
 * Convenience functions for common operations
 */
export async function analyzeHVACIssue(
  description: string,
  customerHistory?: any,
  context?: AuthContext
): Promise<AIAnalysisResponse | null> {
  const response = await goBackendBridge.analyzeCustomerIssue({
    description,
    customerHistory,
    urgencyLevel: 'medium',
  }, context);

  return response.success ? response.data! : null;
}

export async function getSystemHealth(): Promise<SystemHealth | null> {
  try {
    return await goBackendBridge.checkHealth();
  } catch {
    return null;
  }
}

export async function isGoBackendHealthy(): Promise<boolean> {
  const health = goBackendBridge.getHealthStatus();
  return health.status === 'healthy';
}

/**
 * Error handling utilities
 */
export function isBridgeError(error: any): error is GoBackendError {
  return error instanceof Error && error.name === 'GoBackendError';
}

export function formatBridgeError(error: GoBackendError): string {
  return `GoBackend Error (${error.code}): ${error.message}`;
}

export default goBackendBridge;
