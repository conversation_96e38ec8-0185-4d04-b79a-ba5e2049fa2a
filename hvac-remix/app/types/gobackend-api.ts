/**
 * GoBackend-Kratos API Types for HVAC-Remix Integration
 * 
 * Complete type definitions for all GoBackend-Kratos services
 * Ensures type safety across the entire integration
 */

// Base types
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaginationParams {
  limit?: number;
  offset?: number;
  page?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Customer types
export interface Customer extends BaseEntity {
  name: string;
  email?: string;
  phone?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  hvacSystems?: HVACSystem[];
  status: 'active' | 'inactive' | 'suspended';
  tags?: string[];
  notes?: string;
  preferredContactMethod?: 'email' | 'phone' | 'sms';
  serviceHistory?: ServiceRecord[];
}

export interface HVACSystem {
  id: string;
  type: 'heating' | 'cooling' | 'ventilation' | 'combined';
  brand: string;
  model: string;
  serialNumber?: string;
  installationDate?: Date;
  warrantyExpiry?: Date;
  lastMaintenanceDate?: Date;
  nextMaintenanceDate?: Date;
  specifications?: Record<string, any>;
  location?: string;
  status: 'operational' | 'maintenance_required' | 'faulty' | 'offline';
}

// Job types
export interface Job extends BaseEntity {
  customerId: string;
  customer?: Customer;
  title: string;
  description: string;
  status: 'pending' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  type: 'installation' | 'maintenance' | 'repair' | 'inspection' | 'emergency';
  scheduledDate?: Date;
  completedDate?: Date;
  estimatedDuration?: number; // in minutes
  actualDuration?: number; // in minutes
  technicianId?: string;
  technician?: Technician;
  requiredSkills?: string[];
  parts?: JobPart[];
  notes?: JobNote[];
  attachments?: string[];
  location?: {
    address: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  cost?: {
    labor: number;
    parts: number;
    total: number;
    currency: string;
  };
}

export interface JobPart {
  id: string;
  name: string;
  partNumber: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  supplier?: string;
  status: 'ordered' | 'delivered' | 'installed' | 'returned';
}

export interface JobNote {
  id: string;
  content: string;
  authorId: string;
  author?: string;
  createdAt: Date;
  type: 'general' | 'technical' | 'customer_communication' | 'internal';
}

export interface Technician extends BaseEntity {
  name: string;
  email: string;
  phone: string;
  skills: string[];
  certifications: string[];
  status: 'available' | 'busy' | 'off_duty' | 'on_leave';
  currentLocation?: {
    lat: number;
    lng: number;
    timestamp: Date;
  };
  workingHours?: {
    start: string; // HH:mm format
    end: string;   // HH:mm format
    timezone: string;
  };
}

// AI Service types
export interface AIAnalysisRequest {
  description: string;
  customerHistory?: any;
  urgencyLevel?: 'low' | 'medium' | 'high';
  attachments?: string[];
  context?: Record<string, any>;
}

export interface AIAnalysisResponse {
  analysis: string;
  confidence: number; // 0-1
  category: 'heating' | 'cooling' | 'ventilation' | 'electrical' | 'general';
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  suggestedActions: AIAction[];
  estimatedCost?: {
    min: number;
    max: number;
    currency: string;
  };
  estimatedDuration?: number; // in minutes
  requiredSkills?: string[];
  recommendedParts?: string[];
}

export interface AIAction {
  type: 'create_job' | 'schedule_maintenance' | 'order_parts' | 'contact_customer' | 'escalate';
  description: string;
  priority: number; // 1-10
  parameters?: Record<string, any>;
}

export interface AIChatRequest {
  message: string;
  context?: any;
  conversationId?: string;
  model?: 'gemma-3-4b-it' | 'bielik-v3';
}

export interface AIChatResponse {
  response: string;
  conversationId: string;
  confidence: number;
  model: string;
  tokensUsed: number;
  processingTime: number; // in milliseconds
}

export interface AIMaintenancePrediction {
  deviceId: string;
  predictedMaintenanceDate: Date;
  confidence: number;
  riskFactors: string[];
  recommendedActions: string[];
  estimatedCost?: number;
}

export interface AIRouteOptimization {
  optimizedRoute: Array<{
    jobId: string;
    order: number;
    estimatedArrival: Date;
    travelTime: number; // in minutes
    distance: number; // in kilometers
  }>;
  totalDistance: number;
  totalTime: number;
  fuelSavings?: number;
  efficiency: number; // 0-1
}

// Email Service types
export interface Email extends BaseEntity {
  from: string;
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  htmlBody?: string;
  attachments?: EmailAttachment[];
  status: 'unprocessed' | 'processed' | 'archived' | 'spam';
  category?: 'service_request' | 'complaint' | 'inquiry' | 'billing' | 'general';
  priority: 'low' | 'medium' | 'high';
  customerId?: string;
  jobId?: string;
  analysis?: EmailAnalysis;
}

export interface EmailAttachment {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  url: string;
}

export interface EmailAnalysis {
  sentiment: 'positive' | 'neutral' | 'negative';
  intent: string;
  urgency: 'low' | 'medium' | 'high';
  category: string;
  extractedInfo: {
    customerName?: string;
    phoneNumber?: string;
    address?: string;
    issueDescription?: string;
    preferredDate?: Date;
  };
  suggestedResponse?: string;
  confidence: number;
}

export interface EmailCampaign extends BaseEntity {
  name: string;
  template: string;
  recipients: string[];
  scheduledDate?: Date;
  sentDate?: Date;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'cancelled';
  stats?: {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    unsubscribed: number;
  };
}

// Analytics types
export interface RealTimeMetrics {
  activeJobs: number;
  jobsTrend: number; // percentage change
  customerSatisfaction: number; // 0-100
  satisfactionTrend: number;
  revenueToday: number;
  revenueTrend: number;
  aiInsights: number;
  insightsTrend: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  lastUpdated: Date;
}

export interface JobMetrics {
  period: {
    start: Date;
    end: Date;
  };
  totalJobs: number;
  completedJobs: number;
  cancelledJobs: number;
  averageDuration: number; // in minutes
  averageResponseTime: number; // in minutes
  customerSatisfaction: number;
  revenue: number;
  byStatus: Record<string, number>;
  byType: Record<string, number>;
  byPriority: Record<string, number>;
}

export interface CustomerSatisfactionMetrics {
  averageRating: number; // 1-5
  totalResponses: number;
  distribution: Record<number, number>; // rating -> count
  trends: Array<{
    date: Date;
    rating: number;
    responses: number;
  }>;
  topComplaints: string[];
  topPraises: string[];
}

// System types
export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  services: Record<string, {
    status: 'up' | 'down' | 'degraded';
    latency?: number;
    lastCheck: Date;
  }>;
  database: {
    status: 'connected' | 'disconnected';
    latency?: number;
    activeConnections: number;
  };
  ai: {
    status: 'available' | 'unavailable';
    model: string;
    latency?: number;
    queueSize: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
    cores: number;
  };
}

// Real-time subscription types
export interface RealTimeUpdate {
  type: 'job_update' | 'customer_update' | 'system_alert' | 'ai_analysis_complete';
  data: any;
  timestamp: Date;
}

export interface JobUpdate {
  jobId: string;
  status: Job['status'];
  progress?: number; // 0-100
  message?: string;
  technicianId?: string;
}

// tRPC Router type definition
export interface GoBackendRouter {
  customer: {
    list: {
      query: (filters?: {
        search?: string;
        status?: Customer['status'];
        region?: string;
      } & PaginationParams) => Promise<PaginatedResponse<Customer>>;
    };
    get: {
      query: (params: { id: string }) => Promise<Customer>;
    };
    create: {
      mutate: (data: Omit<Customer, keyof BaseEntity>) => Promise<Customer>;
    };
    update: {
      mutate: (data: { id: string } & Partial<Omit<Customer, keyof BaseEntity>>) => Promise<Customer>;
    };
    delete: {
      mutate: (params: { id: string }) => Promise<void>;
    };
  };
  
  job: {
    list: {
      query: (filters?: {
        status?: Job['status'];
        customerId?: string;
        technicianId?: string;
        priority?: Job['priority'];
        dateFrom?: Date;
        dateTo?: Date;
      } & PaginationParams) => Promise<PaginatedResponse<Job>>;
    };
    get: {
      query: (params: { id: string }) => Promise<Job>;
    };
    create: {
      mutate: (data: Omit<Job, keyof BaseEntity | 'customer' | 'technician'>) => Promise<Job>;
    };
    updateStatus: {
      mutate: (params: { id: string; status: Job['status'] }) => Promise<Job>;
    };
    assignTechnician: {
      mutate: (params: { jobId: string; technicianId: string }) => Promise<Job>;
    };
  };
  
  ai: {
    analyzeIssue: {
      mutate: (data: AIAnalysisRequest) => Promise<AIAnalysisResponse>;
    };
    chat: {
      mutate: (data: AIChatRequest) => Promise<AIChatResponse>;
    };
    generateResponse: {
      mutate: (data: {
        customerMessage: string;
        context: any;
        tone?: 'professional' | 'friendly' | 'technical';
      }) => Promise<{ response: string; confidence: number }>;
    };
    predictMaintenance: {
      mutate: (data: {
        deviceId: string;
        deviceType: string;
        lastMaintenanceDate?: Date;
        usageData?: any;
      }) => Promise<AIMaintenancePrediction>;
    };
    optimizeRoute: {
      mutate: (data: {
        jobs: Array<{ id: string; location: any; priority: number }>;
        technicianLocation: any;
        constraints?: any;
      }) => Promise<AIRouteOptimization>;
    };
  };
  
  email: {
    list: {
      query: (filters?: {
        status?: Email['status'];
        category?: string;
        priority?: Email['priority'];
        dateFrom?: Date;
        dateTo?: Date;
      } & PaginationParams) => Promise<PaginatedResponse<Email>>;
    };
    analyze: {
      mutate: (params: { emailId: string }) => Promise<EmailAnalysis>;
    };
    send: {
      mutate: (data: {
        to: string[];
        subject: string;
        body: string;
        template?: string;
        attachments?: string[];
      }) => Promise<{ messageId: string; status: string }>;
    };
    createCampaign: {
      mutate: (data: Omit<EmailCampaign, keyof BaseEntity | 'stats'>) => Promise<EmailCampaign>;
    };
  };
  
  analytics: {
    getRealTimeMetrics: {
      query: () => Promise<RealTimeMetrics>;
    };
    getJobMetrics: {
      query: (filters?: {
        dateFrom?: Date;
        dateTo?: Date;
        groupBy?: 'day' | 'week' | 'month';
      }) => Promise<JobMetrics>;
    };
    getCustomerSatisfaction: {
      query: (filters?: {
        dateFrom?: Date;
        dateTo?: Date;
        customerId?: string;
      }) => Promise<CustomerSatisfactionMetrics>;
    };
    getRevenue: {
      query: (filters?: {
        dateFrom?: Date;
        dateTo?: Date;
        groupBy?: 'day' | 'week' | 'month';
      }) => Promise<{ revenue: number; period: string }[]>;
    };
  };
  
  system: {
    health: {
      query: () => Promise<SystemHealth>;
    };
  };
  
  realtime: {
    subscribe: {
      useSubscription: () => RealTimeUpdate;
    };
    jobUpdates: {
      useSubscription: (params?: { jobId?: string }) => JobUpdate;
    };
  };
}
