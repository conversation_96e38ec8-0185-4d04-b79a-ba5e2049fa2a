/**
 * GoBackend-Kratos API Integration Route
 * 
 * Provides tRPC-style API endpoints that proxy to GoBackend-Kratos services
 * Handles authentication, validation, and error handling
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { goBackendBridge } from "~/services/gobackend-bridge.server";
import type { 
  Customer, 
  Job, 
  AIAnalysisRequest,
  Email 
} from "~/types/gobackend-api";

/**
 * Authentication helper
 */
async function getAuthContext(request: Request) {
  const authHeader = request.headers.get("authorization");
  const token = authHeader?.replace("Bearer ", "");
  
  // TODO: Implement proper JWT validation
  return {
    userId: "user_123", // Extract from JWT
    token,
    roles: ["manager"], // Extract from JWT
  };
}

/**
 * Error response helper
 */
function errorResponse(message: string, status = 500) {
  return json({ error: message }, { status });
}

/**
 * Success response helper
 */
function successResponse(data: any, metadata?: any) {
  return json({ 
    success: true, 
    data, 
    metadata: {
      timestamp: new Date().toISOString(),
      ...metadata
    }
  });
}

/**
 * GET /api/gobackend - Health check and system status
 */
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const url = new URL(request.url);
    const endpoint = url.searchParams.get("endpoint");
    const authContext = await getAuthContext(request);

    switch (endpoint) {
      case "health":
        const health = await goBackendBridge.checkHealth();
        return successResponse(health);

      case "metrics":
        const metricsResponse = await goBackendBridge.getRealTimeMetrics(authContext);
        if (!metricsResponse.success) {
          return errorResponse(metricsResponse.error?.message || "Failed to fetch metrics");
        }
        return successResponse(metricsResponse.data, metricsResponse.metadata);

      case "customers":
        const search = url.searchParams.get("search") || undefined;
        const status = url.searchParams.get("status") as Customer['status'] || undefined;
        const limit = parseInt(url.searchParams.get("limit") || "20");
        const offset = parseInt(url.searchParams.get("offset") || "0");

        const customersResponse = await goBackendBridge.getCustomers({
          search,
          status,
          limit,
          offset,
        }, authContext);

        if (!customersResponse.success) {
          return errorResponse(customersResponse.error?.message || "Failed to fetch customers");
        }
        return successResponse(customersResponse.data, customersResponse.metadata);

      case "jobs":
        const jobStatus = url.searchParams.get("status") as Job['status'] || undefined;
        const customerId = url.searchParams.get("customerId") || undefined;
        const priority = url.searchParams.get("priority") as Job['priority'] || undefined;
        const jobLimit = parseInt(url.searchParams.get("limit") || "20");
        const jobOffset = parseInt(url.searchParams.get("offset") || "0");

        const jobsResponse = await goBackendBridge.getJobs({
          status: jobStatus,
          customerId,
          priority,
          limit: jobLimit,
          offset: jobOffset,
        }, authContext);

        if (!jobsResponse.success) {
          return errorResponse(jobsResponse.error?.message || "Failed to fetch jobs");
        }
        return successResponse(jobsResponse.data, jobsResponse.metadata);

      case "emails":
        const emailStatus = url.searchParams.get("status") as Email['status'] || undefined;
        const category = url.searchParams.get("category") || undefined;
        const emailLimit = parseInt(url.searchParams.get("limit") || "20");
        const emailOffset = parseInt(url.searchParams.get("offset") || "0");

        const emailsResponse = await goBackendBridge.getEmails({
          status: emailStatus,
          category,
          limit: emailLimit,
          offset: emailOffset,
        }, authContext);

        if (!emailsResponse.success) {
          return errorResponse(emailsResponse.error?.message || "Failed to fetch emails");
        }
        return successResponse(emailsResponse.data, emailsResponse.metadata);

      default:
        return errorResponse("Invalid endpoint", 400);
    }
  } catch (error) {
    console.error("GoBackend API error:", error);
    return errorResponse(
      error instanceof Error ? error.message : "Internal server error"
    );
  }
}

/**
 * POST /api/gobackend - Create/Update operations
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    const authContext = await getAuthContext(request);
    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case "createCustomer":
        const customerResponse = await goBackendBridge.createCustomer(data, authContext);
        if (!customerResponse.success) {
          return errorResponse(customerResponse.error?.message || "Failed to create customer");
        }
        return successResponse(customerResponse.data, customerResponse.metadata);

      case "updateCustomer":
        const { id: customerId, ...customerData } = data;
        const updateCustomerResponse = await goBackendBridge.updateCustomer(
          customerId, 
          customerData, 
          authContext
        );
        if (!updateCustomerResponse.success) {
          return errorResponse(updateCustomerResponse.error?.message || "Failed to update customer");
        }
        return successResponse(updateCustomerResponse.data, updateCustomerResponse.metadata);

      case "createJob":
        const jobResponse = await goBackendBridge.createJob(data, authContext);
        if (!jobResponse.success) {
          return errorResponse(jobResponse.error?.message || "Failed to create job");
        }
        return successResponse(jobResponse.data, jobResponse.metadata);

      case "updateJobStatus":
        const { jobId, status } = data;
        const updateJobResponse = await goBackendBridge.updateJobStatus(
          jobId, 
          status, 
          authContext
        );
        if (!updateJobResponse.success) {
          return errorResponse(updateJobResponse.error?.message || "Failed to update job status");
        }
        return successResponse(updateJobResponse.data, updateJobResponse.metadata);

      case "assignTechnician":
        const { jobId: assignJobId, technicianId } = data;
        const assignResponse = await goBackendBridge.assignTechnician(
          assignJobId, 
          technicianId, 
          authContext
        );
        if (!assignResponse.success) {
          return errorResponse(assignResponse.error?.message || "Failed to assign technician");
        }
        return successResponse(assignResponse.data, assignResponse.metadata);

      case "analyzeIssue":
        const analysisRequest: AIAnalysisRequest = data;
        const analysisResponse = await goBackendBridge.analyzeCustomerIssue(
          analysisRequest, 
          authContext
        );
        if (!analysisResponse.success) {
          return errorResponse(analysisResponse.error?.message || "Failed to analyze issue");
        }
        return successResponse(analysisResponse.data, analysisResponse.metadata);

      case "chatWithAI":
        const { message, context, conversationId } = data;
        const chatResponse = await goBackendBridge.chatWithAI({
          message,
          context,
          conversationId,
        }, authContext);
        if (!chatResponse.success) {
          return errorResponse(chatResponse.error?.message || "Failed to chat with AI");
        }
        return successResponse(chatResponse.data, chatResponse.metadata);

      case "generateResponse":
        const { customerMessage, responseContext, tone } = data;
        const generateResponse = await goBackendBridge.generateResponse({
          customerMessage,
          context: responseContext,
          tone,
        }, authContext);
        if (!generateResponse.success) {
          return errorResponse(generateResponse.error?.message || "Failed to generate response");
        }
        return successResponse(generateResponse.data, generateResponse.metadata);

      case "predictMaintenance":
        const { deviceId, deviceType, lastMaintenanceDate, usageData } = data;
        const predictionResponse = await goBackendBridge.predictMaintenance({
          deviceId,
          deviceType,
          lastMaintenanceDate: lastMaintenanceDate ? new Date(lastMaintenanceDate) : undefined,
          usageData,
        }, authContext);
        if (!predictionResponse.success) {
          return errorResponse(predictionResponse.error?.message || "Failed to predict maintenance");
        }
        return successResponse(predictionResponse.data, predictionResponse.metadata);

      case "optimizeRoute":
        const { jobs, technicianLocation, constraints } = data;
        const routeResponse = await goBackendBridge.optimizeRoute({
          jobs,
          technicianLocation,
          constraints,
        }, authContext);
        if (!routeResponse.success) {
          return errorResponse(routeResponse.error?.message || "Failed to optimize route");
        }
        return successResponse(routeResponse.data, routeResponse.metadata);

      case "sendEmail":
        const { to, subject, emailBody, template, attachments } = data;
        const emailResponse = await goBackendBridge.sendEmail({
          to,
          subject,
          body: emailBody,
          template,
          attachments,
        }, authContext);
        if (!emailResponse.success) {
          return errorResponse(emailResponse.error?.message || "Failed to send email");
        }
        return successResponse(emailResponse.data, emailResponse.metadata);

      case "analyzeEmail":
        const { emailId } = data;
        const emailAnalysisResponse = await goBackendBridge.analyzeEmail(emailId, authContext);
        if (!emailAnalysisResponse.success) {
          return errorResponse(emailAnalysisResponse.error?.message || "Failed to analyze email");
        }
        return successResponse(emailAnalysisResponse.data, emailAnalysisResponse.metadata);

      default:
        return errorResponse("Invalid action", 400);
    }
  } catch (error) {
    console.error("GoBackend action error:", error);
    return errorResponse(
      error instanceof Error ? error.message : "Internal server error"
    );
  }
}

/**
 * Utility functions for client-side usage
 */

// Client-side helper for making API calls
export async function callGoBackendAPI(
  endpoint: string, 
  params?: Record<string, string>
): Promise<any> {
  const url = new URL('/api/gobackend', window.location.origin);
  url.searchParams.set('endpoint', endpoint);
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });
  }

  const response = await fetch(url.toString(), {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
    },
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.statusText}`);
  }

  return response.json();
}

export async function callGoBackendAction(
  action: string, 
  data: any
): Promise<any> {
  const response = await fetch('/api/gobackend', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
    },
    body: JSON.stringify({ action, data }),
  });

  if (!response.ok) {
    throw new Error(`Action failed: ${response.statusText}`);
  }

  return response.json();
}

// React hooks for easy data fetching
export function useGoBackendData(endpoint: string, params?: Record<string, string>) {
  const [data, setData] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    callGoBackendAPI(endpoint, params)
      .then(result => {
        setData(result.data);
        setError(null);
      })
      .catch(err => {
        setError(err.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [endpoint, JSON.stringify(params)]);

  return { data, loading, error };
}

export function useGoBackendAction() {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const execute = async (action: string, data: any) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await callGoBackendAction(action, data);
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { execute, loading, error };
}
