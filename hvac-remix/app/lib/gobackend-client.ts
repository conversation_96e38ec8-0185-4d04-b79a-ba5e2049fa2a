/**
 * GoBackend-Kratos API Client for HVAC-Remix
 * 
 * Type-safe client for communicating with GoBackend-Kratos services
 * Provides unified interface for all HVAC operations with AI integration
 */

import { createTRPCProxyClient, httpBatchLink, wsLink, splitLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import type { GoBackendRouter } from '~/types/gobackend-api';

// Environment configuration
const GOBACKEND_URL = process.env.GOBACKEND_URL || 'http://localhost:8080';
const GOBACKEND_WS_URL = process.env.GOBACKEND_WS_URL || 'ws://localhost:8080';

/**
 * Authentication token management
 */
function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
}

/**
 * tRPC React hooks for components
 */
export const trpc = createTRPCReact<GoBackendRouter>();

/**
 * Server-side tRPC client for API routes
 */
export const gobackendClient = createTRPCProxyClient<GoBackendRouter>({
  links: [
    splitLink({
      condition: (op) => op.type === 'subscription',
      true: wsLink({
        url: `${GOBACKEND_WS_URL}/api/trpc`,
        connectionParams: () => ({
          authorization: `Bearer ${getAuthToken()}`,
        }),
      }),
      false: httpBatchLink({
        url: `${GOBACKEND_URL}/api/trpc`,
        headers: () => ({
          authorization: `Bearer ${getAuthToken()}`,
          'content-type': 'application/json',
        }),
        fetch: async (url, options) => {
          const response = await fetch(url, {
            ...options,
            timeout: 30000, // 30 second timeout
          });
          
          if (!response.ok) {
            throw new Error(`GoBackend API Error: ${response.status} ${response.statusText}`);
          }
          
          return response;
        },
      }),
    }),
  ],
});

/**
 * GoBackend API Service Classes
 */

export class CustomerService {
  static async list(filters?: {
    search?: string;
    status?: 'active' | 'inactive';
    region?: string;
    limit?: number;
    offset?: number;
  }) {
    return gobackendClient.customer.list.query(filters);
  }

  static async get(id: string) {
    return gobackendClient.customer.get.query({ id });
  }

  static async create(data: {
    name: string;
    email?: string;
    phone?: string;
    address?: any;
    hvacSystems?: any[];
  }) {
    return gobackendClient.customer.create.mutate(data);
  }

  static async update(id: string, data: Partial<{
    name: string;
    email: string;
    phone: string;
    address: any;
    hvacSystems: any[];
  }>) {
    return gobackendClient.customer.update.mutate({ id, ...data });
  }

  static async delete(id: string) {
    return gobackendClient.customer.delete.mutate({ id });
  }
}

export class JobService {
  static async list(filters?: {
    status?: 'pending' | 'in_progress' | 'completed' | 'cancelled';
    customerId?: string;
    technicianId?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    offset?: number;
  }) {
    return gobackendClient.job.list.query(filters);
  }

  static async get(id: string) {
    return gobackendClient.job.get.query({ id });
  }

  static async create(data: {
    customerId: string;
    title: string;
    description: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    scheduledDate?: Date;
    estimatedDuration?: number;
    requiredSkills?: string[];
  }) {
    return gobackendClient.job.create.mutate(data);
  }

  static async updateStatus(id: string, status: 'pending' | 'in_progress' | 'completed' | 'cancelled') {
    return gobackendClient.job.updateStatus.mutate({ id, status });
  }

  static async assignTechnician(jobId: string, technicianId: string) {
    return gobackendClient.job.assignTechnician.mutate({ jobId, technicianId });
  }
}

export class AIService {
  static async analyzeCustomerIssue(data: {
    description: string;
    customerHistory?: any;
    urgencyLevel?: 'low' | 'medium' | 'high';
    attachments?: string[];
  }) {
    return gobackendClient.ai.analyzeIssue.mutate(data);
  }

  static async chatWithBielik(data: {
    message: string;
    context?: any;
    conversationId?: string;
  }) {
    return gobackendClient.ai.chat.mutate(data);
  }

  static async generateResponse(data: {
    customerMessage: string;
    context: any;
    tone?: 'professional' | 'friendly' | 'technical';
  }) {
    return gobackendClient.ai.generateResponse.mutate(data);
  }

  static async predictMaintenance(data: {
    deviceId: string;
    deviceType: string;
    lastMaintenanceDate?: Date;
    usageData?: any;
  }) {
    return gobackendClient.ai.predictMaintenance.mutate(data);
  }

  static async optimizeRoute(data: {
    jobs: Array<{ id: string; location: any; priority: number }>;
    technicianLocation: any;
    constraints?: any;
  }) {
    return gobackendClient.ai.optimizeRoute.mutate(data);
  }
}

export class EmailService {
  static async list(filters?: {
    status?: 'unprocessed' | 'processed' | 'archived';
    category?: string;
    priority?: 'low' | 'medium' | 'high';
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    offset?: number;
  }) {
    return gobackendClient.email.list.query(filters);
  }

  static async analyze(emailId: string) {
    return gobackendClient.email.analyze.mutate({ emailId });
  }

  static async send(data: {
    to: string[];
    subject: string;
    body: string;
    template?: string;
    attachments?: string[];
  }) {
    return gobackendClient.email.send.mutate(data);
  }

  static async createCampaign(data: {
    name: string;
    template: string;
    recipients: string[];
    scheduledDate?: Date;
  }) {
    return gobackendClient.email.createCampaign.mutate(data);
  }
}

export class AnalyticsService {
  static async getRealTimeMetrics() {
    return gobackendClient.analytics.getRealTimeMetrics.query();
  }

  static async getJobMetrics(filters?: {
    dateFrom?: Date;
    dateTo?: Date;
    groupBy?: 'day' | 'week' | 'month';
  }) {
    return gobackendClient.analytics.getJobMetrics.query(filters);
  }

  static async getCustomerSatisfaction(filters?: {
    dateFrom?: Date;
    dateTo?: Date;
    customerId?: string;
  }) {
    return gobackendClient.analytics.getCustomerSatisfaction.query(filters);
  }

  static async getRevenue(filters?: {
    dateFrom?: Date;
    dateTo?: Date;
    groupBy?: 'day' | 'week' | 'month';
  }) {
    return gobackendClient.analytics.getRevenue.query(filters);
  }
}

/**
 * Real-time WebSocket hooks
 */
export function useRealTimeUpdates() {
  return trpc.realtime.subscribe.useSubscription();
}

export function useJobUpdates(jobId?: string) {
  return trpc.realtime.jobUpdates.useSubscription({ jobId });
}

export function useSystemHealth() {
  return trpc.system.health.useQuery();
}

/**
 * Error handling utilities
 */
export class GoBackendError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number,
    public details?: any
  ) {
    super(message);
    this.name = 'GoBackendError';
  }
}

export function handleGoBackendError(error: any): GoBackendError {
  if (error instanceof GoBackendError) {
    return error;
  }

  return new GoBackendError(
    error.message || 'Unknown GoBackend error',
    error.code || 'UNKNOWN_ERROR',
    error.statusCode || 500,
    error.details
  );
}

/**
 * Health check utility
 */
export async function checkGoBackendHealth(): Promise<{
  status: 'healthy' | 'unhealthy';
  services: Record<string, boolean>;
  latency: number;
}> {
  const startTime = Date.now();
  
  try {
    const health = await gobackendClient.system.health.query();
    const latency = Date.now() - startTime;
    
    return {
      status: health.status,
      services: health.services,
      latency,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      services: {},
      latency: Date.now() - startTime,
    };
  }
}

/**
 * Configuration and initialization
 */
export const gobackendConfig = {
  baseUrl: GOBACKEND_URL,
  wsUrl: GOBACKEND_WS_URL,
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
};

export default {
  client: gobackendClient,
  trpc,
  services: {
    customer: CustomerService,
    job: JobService,
    ai: AIService,
    email: EmailService,
    analytics: AnalyticsService,
  },
  utils: {
    checkHealth: checkGoBackendHealth,
    handleError: handleGoBackendError,
  },
  config: gobackendConfig,
};
