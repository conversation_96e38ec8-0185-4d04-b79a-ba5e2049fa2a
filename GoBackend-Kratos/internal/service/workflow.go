package service

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gobackend-hvac-kratos/internal/biz"
	pb "gobackend-hvac-kratos/api/workflow/v1"
)

// ⚡ Workflow Service - Advanced Process Automation
// GoBackend-Kratos HVAC CRM System

// WorkflowService provides advanced workflow automation functionality
type WorkflowService struct {
	uc  *biz.WorkflowUsecase
	log *log.Helper
}

// NewWorkflowService creates a new workflow service instance
func NewWorkflowService(uc *biz.WorkflowUsecase, logger log.Logger) *WorkflowService {
	return &WorkflowService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

// ============================================================================
// ⚡ SERVICE METHODS
// ============================================================================

// CreateWorkflowRule creates a new workflow rule
func (s *WorkflowService) CreateWorkflowRule(ctx context.Context, rule *biz.WorkflowRule) error {
	s.log.WithContext(ctx).Infof("⚡ Creating workflow rule: %s", rule.RuleName)
	return s.uc.CreateWorkflowRule(ctx, rule)
}

// GetWorkflowRules returns workflow rules with optional filtering
func (s *WorkflowService) GetWorkflowRules(ctx context.Context, triggerType string, isActive *bool) ([]*biz.WorkflowRule, error) {
	s.log.WithContext(ctx).Infof("⚡ Getting workflow rules (type: %s, active: %v)", triggerType, isActive)
	return s.uc.GetWorkflowRules(ctx, triggerType, isActive)
}

// ExecuteWorkflowsForTrigger executes all applicable workflows for a trigger
func (s *WorkflowService) ExecuteWorkflowsForTrigger(ctx context.Context, triggerType string, entity interface{}, entityID uint) ([]biz.WorkflowResult, error) {
	s.log.WithContext(ctx).Infof("⚡ Executing workflows for trigger: %s (entity ID: %d)", triggerType, entityID)
	return s.uc.ExecuteWorkflowsForTrigger(ctx, triggerType, entity, entityID)
}

// GetWorkflowTemplates returns available workflow templates
func (s *WorkflowService) GetWorkflowTemplates(ctx context.Context, category string) ([]*biz.WorkflowTemplate, error) {
	s.log.WithContext(ctx).Infof("⚡ Getting workflow templates (category: %s)", category)
	return s.uc.GetWorkflowTemplates(ctx, category)
}

// createWorkflowFromTemplateInternal creates a workflow rule from a template (helper method)
func (s *WorkflowService) createWorkflowFromTemplateInternal(ctx context.Context, templateID uint, ruleName string, customizations map[string]interface{}) (*biz.WorkflowRule, error) {
	s.log.WithContext(ctx).Infof("⚡ Creating workflow from template ID: %d", templateID)
	return s.uc.CreateWorkflowFromTemplate(ctx, templateID, ruleName, customizations)
}

// HealthCheck performs a health check on the workflow service
func (s *WorkflowService) HealthCheck(ctx context.Context) error {
	s.log.WithContext(ctx).Info("⚡ Performing workflow service health check")

	// Test basic functionality by getting workflow rules
	_, err := s.uc.GetWorkflowRules(ctx, "", nil)
	if err != nil {
		s.log.WithContext(ctx).Errorf("❌ Workflow service health check failed: %v", err)
		return err
	}

	s.log.WithContext(ctx).Info("✅ Workflow service health check passed")
	return nil
}

// ============================================================================
// 🔌 PROTOBUF INTERFACE IMPLEMENTATIONS
// ============================================================================

// CreateWorkflowFromTemplate - protobuf interface implementation
func (s *WorkflowService) CreateWorkflowFromTemplate(ctx context.Context, req *pb.CreateWorkflowFromTemplateRequest) (*pb.CreateWorkflowFromTemplateResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Creating workflow from template (protobuf)")

	// Convert protobuf customizations to map
	customizations := convertStructToMap(req.Customizations)

	rule, err := s.createWorkflowFromTemplateInternal(ctx, uint(req.TemplateId), req.RuleName, customizations)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	pbRule := &pb.WorkflowRule{
		Id:          uint32(rule.ID),
		RuleName:    rule.RuleName,
		TriggerType: rule.TriggerType,
		IsActive:    rule.IsActive,
		Priority:    int32(rule.Priority),
		Description: rule.Description,
		CreatedAt:   timestamppb.New(rule.CreatedAt),
		UpdatedAt:   timestamppb.New(rule.UpdatedAt),
	}

	return &pb.CreateWorkflowFromTemplateResponse{
		Success: true,
		Message: "Workflow created from template successfully",
		Rule:    pbRule,
	}, nil
}

// ============================================================================
// 🔧 HELPER FUNCTIONS
// ============================================================================

// convertInterfaceToString converts interface{} to string
func convertInterfaceToString(v interface{}) string {
	if v == nil {
		return ""
	}
	return fmt.Sprintf("%v", v)
}