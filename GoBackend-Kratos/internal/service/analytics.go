package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gobackend-hvac-kratos/internal/biz"
	pb "gobackend-hvac-kratos/api/analytics/v1"
)

// 📊 Analytics Service - Advanced Dashboard Analytics
// GoBackend-Kratos HVAC CRM System

// AnalyticsService provides advanced analytics and dashboard functionality
type AnalyticsService struct {
	uc  *biz.AnalyticsUsecase
	log *log.Helper
}

// NewAnalyticsService creates a new analytics service instance
func NewAnalyticsService(uc *biz.AnalyticsUsecase, logger log.Logger) *AnalyticsService {
	return &AnalyticsService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

// ============================================================================
// 📊 SERVICE METHODS (Non-protobuf helpers)
// ============================================================================

// CalculateCustomerAnalytics calculates and updates customer analytics
func (s *AnalyticsService) CalculateCustomerAnalytics(ctx context.Context, customerID uint) error {
	s.log.WithContext(ctx).Infof("📊 Calculating customer analytics for ID: %d", customerID)
	return s.uc.CalculateCustomerAnalytics(ctx, customerID)
}

// UpdateRevenueAnalytics updates revenue analytics
func (s *AnalyticsService) UpdateRevenueAnalytics(ctx context.Context, date time.Time, category string, revenue float64, jobsCount int) error {
	s.log.WithContext(ctx).Infof("📊 Updating revenue analytics: %.2f for %s", revenue, date.Format("2006-01-02"))
	return s.uc.UpdateRevenueAnalytics(ctx, date, category, revenue, jobsCount)
}

// UpdateOperationalAnalytics updates operational analytics
func (s *AnalyticsService) UpdateOperationalAnalytics(ctx context.Context, date time.Time, data *biz.OperationalAnalytics) error {
	s.log.WithContext(ctx).Infof("📊 Updating operational analytics for %s", date.Format("2006-01-02"))
	return s.uc.UpdateOperationalAnalytics(ctx, date, data)
}

// healthCheckInternal performs internal health check (helper method)
func (s *AnalyticsService) healthCheckInternal(ctx context.Context) error {
	s.log.WithContext(ctx).Info("📊 Performing analytics service health check")

	// Test basic functionality by getting real-time metrics
	_, err := s.uc.GetRealTimeMetrics(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("❌ Analytics service health check failed: %v", err)
		return err
	}

	s.log.WithContext(ctx).Info("✅ Analytics service health check passed")
	return nil
}

// ============================================================================
// 🔌 PROTOBUF INTERFACE IMPLEMENTATIONS
// ============================================================================

// GetExecutiveDashboard - protobuf interface implementation
func (s *AnalyticsService) GetExecutiveDashboard(ctx context.Context, req *pb.GetExecutiveDashboardRequest) (*pb.GetExecutiveDashboardResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting executive dashboard (protobuf)")

	data, err := s.uc.GetExecutiveDashboard(ctx)
	if err != nil {
		return nil, err
	}

	// Convert biz model to protobuf
	pbData := &pb.ExecutiveDashboardSummary{
		Period:            data.Period,
		TodayRevenue:      data.TodayRevenue,
		TodayJobs:         int32(data.TodayJobs),
		TodaySatisfaction: data.TodaySatisfaction,
		TodayEfficiency:   data.TodayEfficiency,
		WeekRevenue:       data.WeekRevenue,
		WeekJobs:          int32(data.WeekJobs),
		MonthRevenue:      data.MonthRevenue,
		MonthJobs:         int32(data.MonthJobs),
	}

	return &pb.GetExecutiveDashboardResponse{
		DashboardType: "executive",
		LastUpdated:   timestamppb.Now(),
		Data:          pbData,
		Widgets:       []*pb.DashboardWidget{}, // TODO: Convert widgets
	}, nil
}

// GetCustomerInsightsDashboard - protobuf interface implementation
func (s *AnalyticsService) GetCustomerInsightsDashboard(ctx context.Context, req *pb.GetCustomerInsightsDashboardRequest) (*pb.GetCustomerInsightsDashboardResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting customer insights dashboard (protobuf)")

	insights, err := s.uc.GetCustomerInsights(ctx)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	var pbData []*pb.CustomerInsightsDashboard
	for _, insight := range insights {
		pbData = append(pbData, &pb.CustomerInsightsDashboard{
			LoyaltyTier:      insight.LoyaltyTier,
			CustomerCount:    int32(insight.CustomerCount),
			AvgLifetimeValue: insight.AvgLifetimeValue,
			AvgSatisfaction:  insight.AvgSatisfaction,
			AvgChurnRisk:     insight.AvgChurnRisk,
			TierTotalRevenue: insight.TierTotalRevenue,
		})
	}

	return &pb.GetCustomerInsightsDashboardResponse{
		DashboardType: "customer_insights",
		LastUpdated:   timestamppb.Now(),
		Data:          pbData,
		Widgets:       []*pb.DashboardWidget{}, // TODO: Convert widgets
	}, nil
}

// GetOperationalDashboard - protobuf interface implementation
func (s *AnalyticsService) GetOperationalDashboard(ctx context.Context, req *pb.GetOperationalDashboardRequest) (*pb.GetOperationalDashboardResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting operational dashboard (protobuf)")

	data, err := s.uc.GetOperationalDashboard(ctx)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	pbData := &pb.OperationalAnalytics{
		Date:                    timestamppb.New(data.AnalysisDate),
		TotalJobs:               int32(data.TotalActiveJobs),
		CompletedJobs:           int32(data.CompletedJobs),
		PendingJobs:             int32(data.EmergencyJobs), // Using emergency jobs as pending
		CancelledJobs:           int32(data.CancelledJobs),
		AvgCompletionTime:       convertDurationToFloat(data.AverageCompletionTime),
		AvgResponseTime:         convertDurationToFloat(data.AverageResponseTime),
		CustomerSatisfaction:    convertFloatPtr(data.CustomerSatisfaction),
		TechnicianUtilization:   convertFloatPtr(data.TechnicianEfficiency),
		EquipmentEfficiency:     convertFloatPtr(data.EquipmentUtilization),
		FirstCallResolutionRate: convertFloatPtr(data.FirstTimeFixRate),
	}

	return &pb.GetOperationalDashboardResponse{
		DashboardType: "operational",
		LastUpdated:   timestamppb.Now(),
		Data:          pbData,
		Widgets:       []*pb.DashboardWidget{}, // TODO: Convert widgets
	}, nil
}

// GetPerformanceTrends - protobuf interface implementation
func (s *AnalyticsService) GetPerformanceTrends(ctx context.Context, req *pb.GetPerformanceTrendsRequest) (*pb.GetPerformanceTrendsResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting performance trends (protobuf)")

	trends, err := s.uc.GetPerformanceTrends(ctx, int(req.Weeks))
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	var pbTrends []*pb.PerformanceTrend
	for _, trend := range trends {
		// Extract week and year from WeekStart
		year, week := trend.WeekStart.ISOWeek()
		pbTrends = append(pbTrends, &pb.PerformanceTrend{
			Week:                 int32(week),
			Year:                 int32(year),
			Revenue:              0.0, // Not available in biz model
			JobsCompleted:        int32(trend.TotalJobs),
			CustomerSatisfaction: trend.AvgSatisfaction,
			Efficiency:           trend.AvgEfficiency,
			GrowthRate:           0.0, // Not available in biz model
		})
	}

	return &pb.GetPerformanceTrendsResponse{
		Trends: pbTrends,
	}, nil
}

// GetKPIs - protobuf interface implementation
func (s *AnalyticsService) GetKPIs(ctx context.Context, req *pb.GetKPIsRequest) (*pb.GetKPIsResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting KPIs (protobuf)")

	kpis, err := s.uc.GetKPIs(ctx, req.Category)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	var pbKPIs []*pb.KPI
	for _, kpi := range kpis {
		pbKPI := &pb.KPI{
			Name:        kpi.Name,
			Category:    kpi.Category,
			Value:       kpi.Value,
			Unit:        kpi.Unit,
			Description: kpi.Description,
			LastUpdated: timestamppb.New(kpi.LastUpdated),
		}
		if kpi.Target != nil {
			pbKPI.Target = *kpi.Target
		}
		pbKPIs = append(pbKPIs, pbKPI)
	}

	return &pb.GetKPIsResponse{
		Kpis: pbKPIs,
	}, nil
}

// UpdateKPI - protobuf interface implementation
func (s *AnalyticsService) UpdateKPI(ctx context.Context, req *pb.UpdateKPIRequest) (*pb.UpdateKPIResponse, error) {
	s.log.WithContext(ctx).Info("📊 Updating KPI (protobuf)")

	var target *float64
	if req.Target != 0 {
		target = &req.Target
	}

	err := s.uc.UpdateKPI(ctx, req.Name, req.Category, req.Value, target)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateKPIResponse{
		Success: true,
		Message: "KPI updated successfully",
	}, nil
}

// GetRealTimeMetrics - protobuf interface implementation
func (s *AnalyticsService) GetRealTimeMetrics(ctx context.Context, req *pb.GetRealTimeMetricsRequest) (*pb.GetRealTimeMetricsResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting real-time metrics (protobuf)")

	metrics, err := s.uc.GetRealTimeMetrics(ctx)
	if err != nil {
		return nil, err
	}

	// Convert map to protobuf struct
	pbMetrics := make(map[string]float64)
	for key, value := range metrics {
		if floatVal, ok := value.(float64); ok {
			pbMetrics[key] = floatVal
		}
	}

	return &pb.GetRealTimeMetricsResponse{
		Metrics:     pbMetrics,
		LastUpdated: timestamppb.Now(),
	}, nil
}

// GetDashboardWidgets - protobuf interface implementation
func (s *AnalyticsService) GetDashboardWidgets(ctx context.Context, req *pb.GetDashboardWidgetsRequest) (*pb.GetDashboardWidgetsResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting dashboard widgets (protobuf)")

	widgets, err := s.uc.GetDashboardWidgets(ctx, req.Category)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	var pbWidgets []*pb.DashboardWidget
	for _, widget := range widgets {
		pbWidgets = append(pbWidgets, &pb.DashboardWidget{
			WidgetId:    int32(widget.WidgetID),
			WidgetName:  widget.WidgetName,
			WidgetType:  widget.WidgetType,
			Category:    widget.Category,
			Position:    int32(widget.Position),
			Size:        widget.Size,
			Config:      widget.Config,
			LastUpdated: timestamppb.New(widget.LastUpdated),
		})
	}

	return &pb.GetDashboardWidgetsResponse{
		Widgets: pbWidgets,
	}, nil
}

// CreateDashboardWidget - protobuf interface implementation
func (s *AnalyticsService) CreateDashboardWidget(ctx context.Context, req *pb.CreateDashboardWidgetRequest) (*pb.CreateDashboardWidgetResponse, error) {
	s.log.WithContext(ctx).Info("📊 Creating dashboard widget (protobuf)")

	widget := &biz.DashboardWidget{
		WidgetName: req.WidgetName,
		WidgetType: req.WidgetType,
		Category:   req.Category,
		Position:   int(req.Position),
		Size:       req.Size,
		Config:     req.Config,
	}

	err := s.uc.CreateDashboardWidget(ctx, widget)
	if err != nil {
		return nil, err
	}

	return &pb.CreateDashboardWidgetResponse{
		Success: true,
		Message: "Dashboard widget created successfully",
		Widget: &pb.DashboardWidget{
			WidgetId:    int32(widget.WidgetID),
			WidgetName:  widget.WidgetName,
			WidgetType:  widget.WidgetType,
			Category:    widget.Category,
			Position:    int32(widget.Position),
			Size:        widget.Size,
			Config:      widget.Config,
			LastUpdated: timestamppb.New(widget.LastUpdated),
		},
	}, nil
}

// HealthCheck - protobuf interface implementation
func (s *AnalyticsService) HealthCheck(ctx context.Context, req *pb.HealthCheckRequest) (*pb.HealthCheckResponse, error) {
	s.log.WithContext(ctx).Info("📊 Analytics service health check (protobuf)")

	err := s.healthCheckInternal(ctx)
	if err != nil {
		return &pb.HealthCheckResponse{
			Status:  "unhealthy",
			Message: err.Error(),
		}, nil
	}

	return &pb.HealthCheckResponse{
		Status:  "healthy",
		Message: "Analytics service is operational",
	}, nil
}

// ============================================================================
// 🔧 HELPER FUNCTIONS
// ============================================================================

// convertDurationToFloat converts *time.Duration to float64 (in hours)
func convertDurationToFloat(d *time.Duration) float64 {
	if d == nil {
		return 0.0
	}
	return d.Hours()
}

// convertFloatPtr converts *float64 to float64
func convertFloatPtr(f *float64) float64 {
	if f == nil {
		return 0.0
	}
	return *f
}